#include "Ganway_Optimized.h"
#include "bsp_system.h"

// 全局循迹控制变量初始化
Track_Control_t track_ctrl = {
    .mode = TRACK_DEFAULT_MODE,
    .state = TRACK_STATE_NORMAL,
    .base_speed = TRACK_BASE_SPEED_NORMAL,
    .line_position = CENTER_POSITION,
    .error = 0,
    .last_error = 0,
    .lost_count = 0,
    .intersection_count = 0,
    .last_digital = 0
};

// 传感器权重数组（用于加权位置计算）
static const float sensor_weights[SENSOR_COUNT] = {0, 1, 2, 3, 4, 5, 6, 7};

/**
 * @brief 循迹系统初始化
 */
void Track_Init(void)
{
    track_ctrl.mode = TRACK_DEFAULT_MODE;
    track_ctrl.state = TRACK_STATE_NORMAL;
    track_ctrl.base_speed = TRACK_BASE_SPEED_NORMAL;
    track_ctrl.line_position = CENTER_POSITION;
    track_ctrl.error = 0;
    track_ctrl.last_error = 0;
    track_ctrl.lost_count = 0;
    track_ctrl.intersection_count = 0;
    track_ctrl.last_digital = 0;

    // 初始化电机PID参数
    motor_pid.kp = TRACK_PID_KP;
    motor_pid.ki = TRACK_PID_KI;
    motor_pid.kd = TRACK_PID_KD;
    motor_pid.max_integral = TRACK_PID_MAX_INTEGRAL;

    // 速度一致性初始化检查
    #if TRACK_SPEED_CONSISTENCY_CHECK
    // 重置速度监控
    Motor_Reset_Speed_Monitor();

    // 验证差速参数的合理性
    if(TRACK_TURN_DIFF >= track_ctrl.base_speed) {
        // 参数不合理，使用安全默认值
        track_ctrl.base_speed = TRACK_BASE_SPEED_SLOW;
    }
    #endif

    // 初始化转弯预测系统
    #if TRACK_CORNER_PREDICTION_ENABLE
    Reset_Corner_Prediction();
    #endif
}

/**
 * @brief 设置循迹模式
 */
void Track_Set_Mode(Track_Mode_t mode)
{
    track_ctrl.mode = mode;
}

/**
 * @brief 设置基础速度
 */
void Track_Set_Speed(int speed)
{
    if(speed >= TRACK_MIN_SPEED && speed <= TRACK_MAX_SPEED) {
        track_ctrl.base_speed = speed;
    }
}

/**
 * @brief 优化的循迹主函数
 */
void Way_Optimized(unsigned char digital_data, unsigned short *analog_data)
{
    // 分析当前状态
    track_ctrl.state = Analyze_Track_State(digital_data);
    
    // 根据模式选择控制算法
    switch(track_ctrl.mode)
    {
        case TRACK_MODE_BASIC:
            Track_Basic_Control(digital_data);
            break;
            
        case TRACK_MODE_WEIGHTED:
            Track_Weighted_Control(digital_data, analog_data);
            break;
            
        case TRACK_MODE_PID:
            Track_PID_Control(digital_data, analog_data);
            break;
            
        case TRACK_MODE_ADAPTIVE:
            Track_Adaptive_Control(digital_data, analog_data);
            break;
            
        default:
            Track_Weighted_Control(digital_data, analog_data);
            break;
    }
    
    // 更新历史数据
    track_ctrl.last_digital = digital_data;
}

/**
 * @brief 计算黑线位置（加权算法）
 */
float Calculate_Line_Position(unsigned char digital_data, unsigned short *analog_data)
{
    float weighted_sum = 0.0f;
    float total_weight = 0.0f;
    
    // 使用模拟量进行加权计算，提高精度
    for(int i = 0; i < SENSOR_COUNT; i++)
    {
        // 反转数字位（1表示检测到黑线）
        unsigned char sensor_state = (digital_data >> i) & 0x01;
        sensor_state = 1 - sensor_state;  // 反转逻辑
        
        if(sensor_state) {
            // 使用模拟量作为权重，提高精度
            float weight = (float)analog_data[i];
            weighted_sum += sensor_weights[i] * weight;
            total_weight += weight;
        }
    }
    
    // 如果没有检测到黑线，返回上次位置
    if(total_weight == 0.0f) {
        return track_ctrl.line_position;
    }
    
    return weighted_sum / total_weight;
}

/**
 * @brief 计算位置误差
 */
int Calculate_Position_Error(float line_position)
{
    // 计算相对于中心位置的误差
    float error_float = (line_position - CENTER_POSITION) * WEIGHT_FACTOR;
    return (int)error_float;
}

/**
 * @brief 分析循迹状态
 */
Track_State_t Analyze_Track_State(unsigned char digital_data)
{
    unsigned char sensor_count = 0;
    
    // 统计检测到黑线的传感器数量
    for(int i = 0; i < SENSOR_COUNT; i++) {
        if(!((digital_data >> i) & 0x01)) {  // 0表示检测到黑线
            sensor_count++;
        }
    }
    
    // 状态判断逻辑
    if(sensor_count == 0) {
        track_ctrl.lost_count++;
        if(track_ctrl.lost_count > 5) {
            return TRACK_STATE_LOST;
        }
    } else {
        track_ctrl.lost_count = 0;
    }
    
    // 检测十字路口（多个传感器同时检测到）
    if(sensor_count >= 6) {
        return TRACK_STATE_INTERSECTION;
    }
    
    // 检测急转弯
    if((digital_data & 0x07) == 0x00) {  // 左侧3个传感器都检测到
        return TRACK_STATE_TURN_LEFT;
    }
    if((digital_data & 0xE0) == 0x00) {  // 右侧3个传感器都检测到
        return TRACK_STATE_TURN_RIGHT;
    }
    
    return TRACK_STATE_NORMAL;
}

/**
 * @brief 基础控制模式（兼容原有逻辑）
 */
void Track_Basic_Control(unsigned char digital_data)
{
    // 调用原有的Way函数逻辑，但使用优化后的电机控制
    unsigned char sensor0 = 1-(digital_data >> 0) & 0x01;
    unsigned char sensor1 = 1-(digital_data >> 1) & 0x01;
    unsigned char sensor2 = 1-(digital_data >> 2) & 0x01;
    unsigned char sensor3 = 1-(digital_data >> 3) & 0x01;
    unsigned char sensor4 = 1-(digital_data >> 4) & 0x01;
    unsigned char sensor5 = 1-(digital_data >> 5) & 0x01;
    unsigned char sensor6 = 1-(digital_data >> 6) & 0x01;
    unsigned char sensor7 = 1-(digital_data >> 7) & 0x01;

    // 全黑（丢线）处理
    if(sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && 
       sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
        Handle_Lost_Line();
        return;
    }
    
    // 中心位置（直行）- 确保使用统一的速度基准
    if(sensor3 == 1 && sensor4 == 1) {
        Set_PWM(track_ctrl.base_speed, track_ctrl.base_speed);
        return;
    }
    
    // 左转控制
    if(sensor0 == 1 || sensor1 == 1 || sensor2 == 1) {
        Left_Control();
    }
    // 右转控制
    else if(sensor5 == 1 || sensor6 == 1 || sensor7 == 1) {
        Right_Control();
    }
    // 微调控制
    else if(sensor3 == 1) {
        Left_Little_Control();
    }
    else if(sensor4 == 1) {
        Right_Little_Control();
    }
}

/**
 * @brief 加权位置控制模式
 */
void Track_Weighted_Control(unsigned char digital_data, unsigned short *analog_data)
{
    // 计算黑线位置
    track_ctrl.line_position = Calculate_Line_Position(digital_data, analog_data);
    
    // 计算误差
    track_ctrl.error = Calculate_Position_Error(track_ctrl.line_position);
    
    // 使用平滑控制
    Motor_Smooth_Control(track_ctrl.error, track_ctrl.base_speed);
    
    // 更新历史误差
    track_ctrl.last_error = track_ctrl.error;
}

/**
 * @brief PID控制模式
 */
void Track_PID_Control(unsigned char digital_data, unsigned short *analog_data)
{
    // 计算黑线位置
    track_ctrl.line_position = Calculate_Line_Position(digital_data, analog_data);
    
    // 计算误差
    track_ctrl.error = Calculate_Position_Error(track_ctrl.line_position);
    
    // 使用PID控制
    Motor_PID_Control(track_ctrl.error, track_ctrl.base_speed);
    
    // 更新历史误差
    track_ctrl.last_error = track_ctrl.error;
}

/**
 * @brief 自适应控制模式
 */
void Track_Adaptive_Control(unsigned char digital_data, unsigned short *analog_data)
{
    // 根据当前状态自适应选择控制策略
    switch(track_ctrl.state)
    {
        case TRACK_STATE_NORMAL:
            Track_PID_Control(digital_data, analog_data);
            break;
            
        case TRACK_STATE_TURN_LEFT:
        case TRACK_STATE_TURN_RIGHT:
            // 急转弯时降低速度，提高控制精度
            track_ctrl.base_speed = BASE_SPEED * 0.7f;
            Track_PID_Control(digital_data, analog_data);
            break;
            
        case TRACK_STATE_INTERSECTION:
            Handle_Intersection();
            break;
            
        case TRACK_STATE_LOST:
            Handle_Lost_Line();
            break;
            
        default:
            Track_Weighted_Control(digital_data, analog_data);
            break;
    }
}

/**
 * @brief 处理丢线情况（使用统一速度标准）
 */
void Handle_Lost_Line(void)
{
    // 使用基于base_speed的搜索策略，避免硬编码PWM值
    int search_speed = track_ctrl.base_speed * 0.6f;  // 降低搜索速度
    int search_diff = TRACK_TURN_DIFF_LARGE;

    // 根据上次误差方向进行搜索
    if(track_ctrl.last_error > 0) {
        // 上次偏右，向右搜索
        Set_PWM(search_speed - search_diff, search_speed);
    } else if(track_ctrl.last_error < 0) {
        // 上次偏左，向左搜索
        Set_PWM(search_speed, search_speed - search_diff);
    } else {
        // 无历史信息，缓慢前进
        Set_PWM(search_speed, search_speed);
    }
}

/**
 * @brief 处理十字路口
 */
void Handle_Intersection(void)
{
    // 简单处理：直行通过
    Set_PWM(track_ctrl.base_speed, track_ctrl.base_speed);
    delay_ms(100);  // 短暂延时通过路口
}

/**
 * @brief 处理急转弯（使用统一速度标准）
 */
void Handle_Sharp_Turn(int direction)
{
    // 使用基于base_speed的急转弯控制
    int turn_speed = track_ctrl.base_speed * 0.8f;  // 急转弯时适当降速
    int turn_diff = TRACK_TURN_DIFF_LARGE;

    if(direction > 0) {
        // 右转：左轮保持速度，右轮减速
        Set_PWM(turn_speed - turn_diff, turn_speed);
    } else {
        // 左转：右轮保持速度，左轮减速
        Set_PWM(turn_speed, turn_speed - turn_diff);
    }
}

/**
 * @brief 调试信息输出
 */
void Track_Debug_Info(void)
{
#if TRACK_DEBUG_ENABLE && TRACK_DEBUG_OLED_ENABLE
    // 这里可以添加OLED显示调试信息的代码
    // 例如显示当前模式、线位置、误差等
    // OLED_ShowString(0, 0, "Mode:", 12, 1);
    // OLED_ShowNum(30, 0, track_ctrl.mode, 1, 12, 1);
    // OLED_ShowString(0, 12, "Pos:", 12, 1);
    // OLED_ShowSignedNum(24, 12, (int)(track_ctrl.line_position * 10), 2, 12, 1);
    // OLED_ShowString(0, 24, "Err:", 12, 1);
    // OLED_ShowSignedNum(24, 24, track_ctrl.error, 3, 12, 1);
    // OLED_Refresh();
#endif

#if TRACK_DEBUG_ENABLE && TRACK_DEBUG_UART_ENABLE
    // 这里可以添加串口输出调试信息的代码
    // printf("Mode:%d Pos:%.2f Err:%d\r\n",
    //        track_ctrl.mode, track_ctrl.line_position, track_ctrl.error);
#endif
}

/**
 * @brief 传感器校准函数
 */
void Track_Calibrate_Sensors(No_MCU_Sensor *sensor)
{
#if TRACK_AUTO_CALIBRATE_ENABLE
    // 自动校准逻辑
    // 这里可以添加自动校准的实现
    // 例如：采集多组数据，计算平均值等
#endif
}

/**
 * @brief 获取当前循迹状态信息（用于外部监控）
 */
Track_Control_t* Track_Get_Status(void)
{
    return &track_ctrl;
}

/**
 * @brief 设置循迹参数（运行时调整）
 */
void Track_Set_PID_Params(float kp, float ki, float kd)
{
    motor_pid.kp = kp;
    motor_pid.ki = ki;
    motor_pid.kd = kd;
}

/**
 * @brief 重置PID积分项（用于消除积分饱和）
 */
void Track_Reset_PID_Integral(void)
{
    motor_pid.integral = 0;
}

/**
 * @brief 紧急停止函数
 */
void Track_Emergency_Stop(void)
{
    Motor_Stop();
    track_ctrl.state = TRACK_STATE_STOP;
}

/*************************** 转弯预测算法实现 ***************************/

// 位置历史缓冲区和相关变量
static float position_history[TRACK_CORNER_HISTORY_SIZE];
static int history_index = 0;
static int history_count = 0;
static int prediction_delay_counter = 0;
static Corner_Prediction_t last_prediction = CORNER_PREDICTION_NONE;

/**
 * @brief 更新位置历史缓冲区
 * @param position 当前黑线位置
 */
void Update_Position_History(float position)
{
    position_history[history_index] = position;
    history_index = (history_index + 1) % TRACK_CORNER_HISTORY_SIZE;

    if(history_count < TRACK_CORNER_HISTORY_SIZE) {
        history_count++;
    }
}

/**
 * @brief 计算位置变化率
 * @return 位置变化率（绝对值）
 */
float Calculate_Position_Change_Rate(void)
{
    if(history_count < 3) {
        return 0.0f;  // 历史数据不足，返回0
    }

    // 计算最近几个位置的变化率
    float total_change = 0.0f;
    int samples = 0;

    for(int i = 1; i < history_count && i < 4; i++) {
        int current_idx = (history_index - 1 - i + TRACK_CORNER_HISTORY_SIZE) % TRACK_CORNER_HISTORY_SIZE;
        int prev_idx = (history_index - i + TRACK_CORNER_HISTORY_SIZE) % TRACK_CORNER_HISTORY_SIZE;

        float change = position_history[current_idx] - position_history[prev_idx];
        total_change += ABS(change);
        samples++;
    }

    return samples > 0 ? total_change / samples : 0.0f;
}

/**
 * @brief 转弯预测核心算法
 * @param current_position 当前黑线位置
 * @return 预测结果
 */
Corner_Prediction_t Predict_Corner_State(float current_position)
{
#if !TRACK_CORNER_PREDICTION_ENABLE
    return CORNER_PREDICTION_NONE;  // 功能未启用
#endif

    // 更新位置历史
    Update_Position_History(current_position);

    // 计算位置变化率
    float change_rate = Calculate_Position_Change_Rate();

    // 基于变化率的预测逻辑
    Corner_Prediction_t current_prediction = CORNER_PREDICTION_NONE;

    if(change_rate > TRACK_CORNER_PREDICTION_THRESHOLD) {
        // 检测到显著的位置变化，可能即将转弯
        current_prediction = CORNER_PREDICTION_APPROACHING;

        // 进一步分析：如果变化率非常大，可能已经在转弯中
        if(change_rate > TRACK_CORNER_PREDICTION_THRESHOLD * 1.5f) {
            current_prediction = CORNER_PREDICTION_TURNING;
        }
    } else if(change_rate < TRACK_CORNER_PREDICTION_THRESHOLD * 0.5f &&
              last_prediction != CORNER_PREDICTION_NONE) {
        // 变化率降低，可能转弯结束
        current_prediction = CORNER_PREDICTION_EXITING;
    }

    // 防抖动处理：使用延迟计数器
    if(current_prediction != last_prediction) {
        prediction_delay_counter++;
        if(prediction_delay_counter >= TRACK_CORNER_PREDICTION_DELAY) {
            // 延迟足够，更新预测结果
            last_prediction = current_prediction;
            prediction_delay_counter = 0;

#if TRACK_CORNER_DEBUG_ENABLE && TRACK_DEBUG_ENABLE
            // 调试输出（可选）
            // printf("Corner prediction: %d, change_rate: %.2f\n", current_prediction, change_rate);
#endif
        } else {
            // 延迟不足，保持上次预测
            current_prediction = last_prediction;
        }
    } else {
        // 预测结果相同，重置计数器
        prediction_delay_counter = 0;
    }

    return current_prediction;
}

/**
 * @brief 重置转弯预测状态（用于初始化或状态重置）
 */
void Reset_Corner_Prediction(void)
{
    // 清空位置历史
    for(int i = 0; i < TRACK_CORNER_HISTORY_SIZE; i++) {
        position_history[i] = CENTER_POSITION;
    }

    // 重置相关变量
    history_index = 0;
    history_count = 0;
    prediction_delay_counter = 0;
    last_prediction = CORNER_PREDICTION_NONE;
}

/**
 * @brief 获取转弯预测调试信息
 * @param change_rate 输出参数：当前位置变化率
 * @param prediction 输出参数：当前预测结果
 */
void Get_Corner_Prediction_Debug_Info(float *change_rate, Corner_Prediction_t *prediction)
{
    if(change_rate != NULL) {
        *change_rate = Calculate_Position_Change_Rate();
    }
    if(prediction != NULL) {
        *prediction = last_prediction;
    }
}
