#include "motor.h"
#include "Ganway.h"
#include "Ganway_Optimized.h"  // 引入优化算法
#include "bsp_system.h"

// 新增：带模拟量的循迹函数
void Way_With_Analog(unsigned char digital_data, unsigned short *analog_data)
{
    // 使用优化的循迹算法
    Way_Optimized(digital_data, analog_data);
}

// 原有函数保持兼容性
void Way(unsigned char x)
{

    unsigned char sensor0 = 1-(x >> 0) & 0x01;
    unsigned char sensor1 = 1-(x >> 1) & 0x01;
    unsigned char sensor2 = 1-(x >> 2) & 0x01;
    unsigned char sensor3 = 1-(x >> 3) & 0x01;
    unsigned char sensor4 = 1-(x >> 4) & 0x01;
    unsigned char sensor5 = 1-(x >> 5) & 0x01;
    unsigned char sensor6 = 1-(x >> 6) & 0x01;
    unsigned char sensor7 = 1-(x >> 7) & 0x01;

    if(sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0){
        // 修复：丢线处理使用统一速度控制，调用优化的丢线处理函数
        Handle_Lost_Line();
    }
    if(sensor0 == 1 && sensor1 == 1 && sensor2 == 1 && sensor3 == 1 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0){
        // 修复：特殊情况处理使用统一速度控制
        extern Track_Control_t track_ctrl;
        Set_PWM(10,10);
        delay_ms(2);
        // 使用基于base_speed的急转弯控制，避免硬编码高速值
        Handle_Sharp_Turn(-1);  // 左转处理
    }





    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 1 && sensor4 == 1 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
        // 修复：使用统一的base_speed替代硬编码的高速值7000
        extern Track_Control_t track_ctrl;  // 引用全局控制变量
        Set_PWM(track_ctrl.base_speed, track_ctrl.base_speed);
    }

    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 1 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
 
       Right_Control();
    }
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 1 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
 
        Left_Control();
    }







    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 1 && sensor6 == 0 && sensor7 == 0) {

        Right_Little_Control();
    }
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 1 && sensor5 == 1 && sensor6 == 0 && sensor7 == 0) {
        Right_Little_Control();
    }
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 1 && sensor7 == 0) {
        Right_Little_Control();
    }



    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 1) {
        Right_Little_Control();
    }
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 1 && sensor7 == 1) {
        Right_Control();
    }

    else if (sensor0 == 1 && sensor1 == 0 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
       Left_Little_Control();
    }
    else if (sensor0 == 1 && sensor1 == 1 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
        Left_Control();
    }


    else if (sensor0 == 0 && sensor1 == 1 && sensor2 == 0 && sensor3 == 0 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
        Left_Little_Control();
    }
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 0 && sensor3 == 1 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
        Left_Little_Control();
    }
    else if (sensor0 == 0 && sensor1 == 0 && sensor2 == 1 && sensor3 == 1 && sensor4 == 0 && sensor5 == 0 && sensor6 == 0 && sensor7 == 0) {
        Left_Little_Control();
    }





 
    
}
// void Set_PWM(int pwmA,int pwmB);
// void Left_Control(void);
// void Right_Control(void);
// void Left_Large_Control(void);
// void Right_Large_Control(void);
